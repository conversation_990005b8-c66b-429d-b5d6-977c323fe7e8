package com.yeepay.riskguard.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 业务场景参数模型
 */
public class ScenarioParameter {
    
    private Long id;
    
    @JsonProperty("bizCode")
    private String bizCode;
    
    @JsonProperty("scenarioCode")
    private String scenarioCode;
    
    @JsonProperty("stage")
    private String stage;
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("dataType")
    private String dataType;
    
    @JsonProperty("required")
    private Boolean required;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("asPrimaryKey")
    private Boolean asPrimaryKey;
    
    @JsonProperty("asDimension")
    private Boolean asDimension;
    
    @JsonProperty("asMeasure")
    private Boolean asMeasure;
    
    @JsonProperty("asEventTime")
    private Boolean asEventTime;
    
    @JsonProperty("asCondition")
    private Boolean asCondition;
    
    @JsonProperty("scale")
    private Integer scale;
    
    @JsonProperty("precision")
    private Integer precision;
    
    @JsonProperty("nonce")
    private Integer nonce;
    
    // 构造函数
    public ScenarioParameter() {}
    
    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getBizCode() { return bizCode; }
    public void setBizCode(String bizCode) { this.bizCode = bizCode; }
    
    public String getScenarioCode() { return scenarioCode; }
    public void setScenarioCode(String scenarioCode) { this.scenarioCode = scenarioCode; }
    
    public String getStage() { return stage; }
    public void setStage(String stage) { this.stage = stage; }
    
    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDataType() { return dataType; }
    public void setDataType(String dataType) { this.dataType = dataType; }
    
    public Boolean getRequired() { return required; }
    public void setRequired(Boolean required) { this.required = required; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public Boolean getAsPrimaryKey() { return asPrimaryKey; }
    public void setAsPrimaryKey(Boolean asPrimaryKey) { this.asPrimaryKey = asPrimaryKey; }
    
    public Boolean getAsDimension() { return asDimension; }
    public void setAsDimension(Boolean asDimension) { this.asDimension = asDimension; }
    
    public Boolean getAsMeasure() { return asMeasure; }
    public void setAsMeasure(Boolean asMeasure) { this.asMeasure = asMeasure; }
    
    public Boolean getAsEventTime() { return asEventTime; }
    public void setAsEventTime(Boolean asEventTime) { this.asEventTime = asEventTime; }
    
    public Boolean getAsCondition() { return asCondition; }
    public void setAsCondition(Boolean asCondition) { this.asCondition = asCondition; }
    
    public Integer getScale() { return scale; }
    public void setScale(Integer scale) { this.scale = scale; }
    
    public Integer getPrecision() { return precision; }
    public void setPrecision(Integer precision) { this.precision = precision; }
    
    public Integer getNonce() { return nonce; }
    public void setNonce(Integer nonce) { this.nonce = nonce; }
    
    // 工具方法
    public boolean isNumericType() {
        return "Decimal".equalsIgnoreCase(dataType) || 
               "Integer".equalsIgnoreCase(dataType);
    }
    
    public boolean isDateType() {
        return "Date".equalsIgnoreCase(dataType);
    }
    
    public boolean isStringType() {
        return "String".equalsIgnoreCase(dataType);
    }
    
    @Override
    public String toString() {
        return String.format("ScenarioParameter{code='%s', name='%s', dataType='%s', required=%s}", 
                code, name, dataType, required);
    }
}
