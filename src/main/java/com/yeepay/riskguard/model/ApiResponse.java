package com.yeepay.riskguard.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * API响应通用模型
 */
public class ApiResponse<T> {
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("traceId")
    private String traceId;
    
    @JsonProperty("data")
    private T data;
    
    public ApiResponse() {}
    
    public ApiResponse(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    // Getter和Setter方法
    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public String getTraceId() { return traceId; }
    public void setTraceId(String traceId) { this.traceId = traceId; }
    
    public T getData() { return data; }
    public void setData(T data) { this.data = data; }
    
    // 工具方法
    public boolean isSuccess() {
        return "000000".equals(code);
    }
    
    @Override
    public String toString() {
        return String.format("ApiResponse{code='%s', message='%s', traceId='%s'}", 
                code, message, traceId);
    }
}
