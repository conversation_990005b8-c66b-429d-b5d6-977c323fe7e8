package com.yeepay.riskguard.test;

import com.yeepay.riskguard.model.ScenarioParameter;
import com.yeepay.riskguard.tool.RiskGuardParameterTool;

import java.util.List;

/**
 * 参数查询工具测试
 */
public class ParameterToolTest {
    
    public static void main(String[] args) {
        System.out.println("=== 风控中台参数查询工具测试 ===\n");
        
        RiskGuardParameterTool tool = new RiskGuardParameterTool();
        
        try {
            // 测试1：查询PAN场景问询阶段参数
            System.out.println("1. 查询PAN场景问询阶段参数：");
            List<ScenarioParameter> inquiryParams = tool.getParameters("pan", "INQUIRY");
            tool.printParameters(inquiryParams);
            
            // 测试2：查询必填参数
            System.out.println("\n2. 必填参数：");
            List<ScenarioParameter> requiredParams = tool.getRequiredParameters("pan", "INQUIRY", "GPT");
            tool.printParameters(requiredParams);
            
            // 测试3：生成参数示例JSON
            System.out.println("\n3. 参数示例JSON：");
            String exampleJson = tool.generateParameterExample("pan", "INQUIRY", "GPT");
            System.out.println(exampleJson);
            
            // 测试4：测试缓存功能
            System.out.println("\n4. 测试缓存功能（第二次查询应该从缓存获取）：");
            List<ScenarioParameter> cachedParams = tool.getParameters("pan", "INQUIRY");
            System.out.println("缓存测试完成，参数数量: " + cachedParams.size());
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
