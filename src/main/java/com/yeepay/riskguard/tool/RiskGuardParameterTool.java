package com.yeepay.riskguard.tool;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.riskguard.model.ScenarioParameter;
import com.yeepay.riskguard.model.ApiResponse;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 风控中台参数查询MCP工具
 */
public class RiskGuardParameterTool {

    private static final String BASE_URL = "ycetest.yeepay.com:31237";
    private static final String PARAMETER_ENDPOINT = "/riskguard-rules/hessian/rest/model-field/list";

    private final ObjectMapper objectMapper;
    private final Map<String, List<ScenarioParameter>> parameterCache;

    public RiskGuardParameterTool() {
        this.objectMapper = new ObjectMapper();
        this.parameterCache = new ConcurrentHashMap<>();
    }

    /**
     * 获取业务场景参数
     *
     * @param scenarioCode 业务场景编码
     * @param stage 阶段 (INQUIRY/CONFIRM)
     * @param bizCode 业务板块 (默认GPT)
     * @return 参数列表
     */
    public List<ScenarioParameter> getParameters(String scenarioCode, String stage, String bizCode) {
        String cacheKey = String.format("%s_%s_%s", scenarioCode, stage, bizCode);

        // 先检查缓存
        if (parameterCache.containsKey(cacheKey)) {
            System.out.println("从缓存获取参数: " + cacheKey);
            return parameterCache.get(cacheKey);
        }

        try {
            String urlString = String.format("http://%s%s?scenarioCode=%s&stage=%s&bizCode=%s",
                    BASE_URL, PARAMETER_ENDPOINT, scenarioCode, stage, bizCode);

            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            connection.setRequestProperty("Accept", "application/json");

            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                ApiResponse<List<ScenarioParameter>> apiResponse = objectMapper.readValue(
                        response.toString(),
                        new TypeReference<ApiResponse<List<ScenarioParameter>>>() {}
                );

                if ("000000".equals(apiResponse.getCode())) {
                    List<ScenarioParameter> parameters = apiResponse.getData();
                    // 缓存结果
                    parameterCache.put(cacheKey, parameters);

                    System.out.println(String.format("成功获取参数，场景: %s, 阶段: %s, 参数数量: %d",
                            scenarioCode, stage, parameters.size()));

                    return parameters;
                } else {
                    throw new RuntimeException("API返回错误: " + apiResponse.getMessage());
                }
            } else {
                throw new RuntimeException("HTTP请求失败，状态码: " + responseCode);
            }

        } catch (IOException e) {
            throw new RuntimeException("请求参数接口失败", e);
        }
    }

    /**
     * 获取参数（使用默认bizCode=GPT）
     */
    public List<ScenarioParameter> getParameters(String scenarioCode, String stage) {
        return getParameters(scenarioCode, stage, "GPT");
    }

    /**
     * 打印参数信息
     */
    public void printParameters(List<ScenarioParameter> parameters) {
        System.out.println("\n=== 参数列表 ===");
        System.out.printf("%-20s %-30s %-15s %-10s %-10s%n",
                "参数编码", "参数名称", "数据类型", "是否必填", "类型");
        for (int i = 0; i < 90; i++) {
            System.out.print("-");
        }
        System.out.println();

        for (ScenarioParameter param : parameters) {
            System.out.printf("%-20s %-30s %-15s %-10s %-10s%n",
                    param.getCode(),
                    param.getName(),
                    param.getDataType(),
                    param.getRequired() ? "是" : "否",
                    param.getType());
        }
        System.out.println();
    }

    /**
     * 获取必填参数
     */
    public List<ScenarioParameter> getRequiredParameters(String scenarioCode, String stage, String bizCode) {
        List<ScenarioParameter> allParameters = getParameters(scenarioCode, stage, bizCode);
        return allParameters.stream()
                .filter(ScenarioParameter::getRequired)
                .collect(Collectors.toList());
    }

    /**
     * 生成参数示例JSON
     */
    public String generateParameterExample(String scenarioCode, String stage, String bizCode) {
        List<ScenarioParameter> parameters = getParameters(scenarioCode, stage, bizCode);

        StringBuilder json = new StringBuilder("{\n");
        for (int i = 0; i < parameters.size(); i++) {
            ScenarioParameter param = parameters.get(i);
            json.append("  \"").append(param.getCode()).append("\": ");

            // 根据数据类型生成示例值
            String dataType = param.getDataType().toLowerCase();
            if ("string".equals(dataType)) {
                json.append("\"示例值\"");
            } else if ("decimal".equals(dataType)) {
                json.append("100.00");
            } else if ("date".equals(dataType)) {
                json.append("\"2024-01-01 12:00:00\"");
            } else if ("integer".equals(dataType)) {
                json.append("123");
            } else {
                json.append("\"值\"");
            }

            if (i < parameters.size() - 1) {
                json.append(",");
            }
            json.append("  // ").append(param.getName());
            if (param.getRequired()) {
                json.append(" [必填]");
            }
            json.append("\n");
        }
        json.append("}");

        return json.toString();
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        parameterCache.clear();
        System.out.println("参数缓存已清除");
    }
}
