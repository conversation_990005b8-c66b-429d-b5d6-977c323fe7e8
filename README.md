# 风控中台对接MCP工具

## 项目简介
这是一个简单的MCP（Model Context Protocol）工具，用于查询风控中台的请求参数并提供完整的对接代码。

## 功能特性
1. **参数查询工具** - 查询不同业务场景的请求参数
2. **缓存机制** - 自动缓存参数查询结果，提高性能
3. **参数分析** - 支持必填参数筛选、参数示例生成
4. **简洁易用** - 轻量级设计，专注于接口对接

## 已实现功能 ✅

### 1. 参数查询工具
- ✅ 动态查询业务场景参数
- ✅ 支持缓存机制
- ✅ 必填参数筛选
- ✅ 参数示例JSON生成
- ✅ 参数信息格式化输出

### 2. 测试验证
- ✅ 成功查询PAN场景参数
- ✅ 缓存功能正常工作
- ✅ 参数解析正确

## 快速开始

### 1. 编译项目
```bash
cd riskguard-mcp
mvn compile
```

### 2. 运行测试
```bash
mvn exec:java -Dexec.mainClass="com.yeepay.riskguard.test.ParameterToolTest"
```

### 3. 使用MCP工具
```java
// 创建参数查询工具
RiskGuardParameterTool tool = new RiskGuardParameterTool();

// 查询参数
List<ScenarioParameter> parameters = tool.getParameters("pan", "INQUIRY", "GPT");

// 打印参数信息
tool.printParameters(parameters);

// 获取必填参数
List<ScenarioParameter> requiredParams = tool.getRequiredParameters("pan", "INQUIRY", "GPT");

// 生成参数示例JSON
String exampleJson = tool.generateParameterExample("pan", "INQUIRY", "GPT");
```

## 接口说明

### 参数查询接口
- **URL**: `ycetest.yeepay.com:31237/riskguard-rules/hessian/rest/model-field/list`
- **方法**: GET
- **参数**:
  - `scenarioCode`: 业务场景编码（如：pan）
  - `stage`: 阶段（INQUIRY-问询, CONFIRM-确认）
  - `bizCode`: 业务板块（默认：GPT）

### 风控决策接口
需要您提供以下接口信息：
1. **问询接口** - 用于风控问询
2. **确认接口** - 用于风控确认
3. **接口认证方式** - API Key、Token等
4. **请求格式** - JSON结构和必填字段

## 项目结构
```
├── src/main/java/com/yeepay/riskguard/
│   ├── tool/
│   │   └── RiskGuardParameterTool.java     # MCP参数查询工具
│   ├── client/
│   │   └── RiskGuardClient.java            # 风控中台客户端
│   ├── model/
│   │   ├── ScenarioParameter.java          # 参数模型
│   │   └── RiskDecisionResponse.java       # 响应模型
│   └── example/
│       └── RiskGuardExample.java           # 使用示例
```

## 测试结果示例

```
=== 风控中台参数查询工具测试 ===

1. 查询PAN场景问询阶段参数：
成功获取参数，场景: pan, 阶段: INQUIRY, 参数数量: 6

=== 参数列表 ===
参数编码                 参数名称                           数据类型            是否必填       类型
------------------------------------------------------------------------------------------
targetCnyAmount      实际到账金额（人民币）                    Decimal         否          Original
subMerchantId        子商户商编                          String          否          Original
tradeType            交易类型                           String          否          Original
tradeDate            交易时间                           Date            是          Original
merchantId           商户编号                           String          否          Original
amount               交易金额                           Decimal         否          Original

2. 必填参数：
=== 参数列表 ===
参数编码                 参数名称                           数据类型            是否必填       类型
------------------------------------------------------------------------------------------
tradeDate            交易时间                           Date            是          Original

3. 参数示例JSON：
{
  "targetCnyAmount": 100.00,  // 实际到账金额（人民币）
  "subMerchantId": "示例值",  // 子商户商编
  "tradeType": "示例值",  // 交易类型
  "tradeDate": "2024-01-01 12:00:00",  // 交易时间 [必填]
  "merchantId": "示例值",  // 商户编号
  "amount": 100.00  // 交易金额
}
```

## 需要您提供的接口信息

为了完成完整的对接代码，请提供：

1. **风控问询接口**
   - 接口地址
   - 请求方法（POST/GET）
   - 请求参数格式
   - 响应格式

2. **风控确认接口**
   - 接口地址
   - 请求方法
   - 请求参数格式
   - 响应格式

3. **认证信息**
   - API Key
   - Token获取方式
   - 请求头要求

4. **业务场景**
   - 支持的scenarioCode列表
   - 各场景的业务含义
   - 特殊处理要求

## 技术特点

- **Java 8兼容** - 使用Java 8语法，兼容性好
- **轻量级依赖** - 仅依赖Jackson进行JSON处理
- **缓存优化** - 自动缓存参数查询结果
- **错误处理** - 完善的异常处理机制
- **易于扩展** - 模块化设计，便于功能扩展
