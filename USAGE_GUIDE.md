# 风控中台对接MCP工具使用指南

## 概述

本工具已成功实现风控中台参数查询功能，可以动态获取不同业务场景的请求参数，并提供完整的参数分析和示例生成功能。

## 核心功能

### 1. 参数查询 (RiskGuardParameterTool)

#### 基本用法
```java
RiskGuardParameterTool tool = new RiskGuardParameterTool();

// 查询参数
List<ScenarioParameter> parameters = tool.getParameters("pan", "INQUIRY", "GPT");
```

#### 支持的方法
- `getParameters(scenarioCode, stage, bizCode)` - 查询指定场景参数
- `getParameters(scenarioCode, stage)` - 使用默认bizCode查询
- `getRequiredParameters(scenarioCode, stage, bizCode)` - 获取必填参数
- `printParameters(parameters)` - 格式化打印参数信息
- `generateParameterExample(scenarioCode, stage, bizCode)` - 生成参数示例JSON
- `clearCache()` - 清除缓存

### 2. 参数模型 (ScenarioParameter)

每个参数包含以下信息：
- `code` - 参数编码
- `name` - 参数名称
- `dataType` - 数据类型 (String, Decimal, Date, Integer)
- `required` - 是否必填
- `type` - 参数类型
- `asPrimaryKey` - 是否为主键
- `asDimension` - 是否为维度
- `asMeasure` - 是否为度量
- `asEventTime` - 是否为事件时间
- `asCondition` - 是否为条件

## 实际测试结果

### PAN场景问询阶段参数
```
参数编码                 参数名称                           数据类型            是否必填       类型        
------------------------------------------------------------------------------------------
targetCnyAmount      实际到账金额（人民币）                    Decimal         否          Original  
subMerchantId        子商户商编                          String          否          Original  
tradeType            交易类型                           String          否          Original  
tradeDate            交易时间                           Date            是          Original  
merchantId           商户编号                           String          否          Original  
amount               交易金额                           Decimal         否          Original  
```

### 生成的参数示例
```json
{
  "targetCnyAmount": 100.00,  // 实际到账金额（人民币）
  "subMerchantId": "示例值",  // 子商户商编
  "tradeType": "示例值",  // 交易类型
  "tradeDate": "2024-01-01 12:00:00",  // 交易时间 [必填]
  "merchantId": "示例值",  // 商户编号
  "amount": 100.00  // 交易金额
}
```

## 接下来的开发步骤

为了完成完整的风控中台对接，您需要提供以下信息：

### 1. 风控问询接口
- **接口地址**: `http://ycetest.yeepay.com:31237/riskguard/inquiry` (请确认)
- **请求方法**: POST (请确认)
- **请求头**: 
  ```
  Content-Type: application/json
  Authorization: Bearer YOUR_TOKEN (请提供认证方式)
  ```
- **请求体格式**: 
  ```json
  {
    "scenarioCode": "pan",
    "stage": "INQUIRY", 
    "bizCode": "GPT",
    "data": {
      // 业务数据，基于参数查询结果构建
    }
  }
  ```
- **响应格式**: 请提供示例响应

### 2. 风控确认接口
- **接口地址**: `http://ycetest.yeepay.com:31237/riskguard/confirm` (请确认)
- **请求方法**: POST (请确认)
- **请求体格式**: 
  ```json
  {
    "scenarioCode": "pan",
    "stage": "CONFIRM",
    "bizCode": "GPT", 
    "data": {
      // 业务数据
    },
    "inquiryResult": {
      // 问询接口返回的结果
    }
  }
  ```
- **响应格式**: 请提供示例响应

### 3. 认证信息
- API Key或Token获取方式
- 请求头中的认证字段名称
- Token是否有过期时间

### 4. 业务场景信息
- 除了"pan"之外，还支持哪些scenarioCode？
- 各个场景的业务含义是什么？
- 是否有特殊的处理逻辑？

## 扩展建议

一旦您提供了上述信息，我可以继续完善：

1. **风控客户端** - 实现完整的问询和确认接口调用
2. **认证管理** - 实现Token管理和自动刷新
3. **错误处理** - 完善的异常处理和重试机制
4. **配置管理** - 支持不同环境的配置
5. **日志记录** - 详细的调用日志和监控
6. **单元测试** - 完整的测试用例

## 当前项目状态

✅ **已完成**:
- 参数查询工具
- 缓存机制
- 参数分析功能
- 示例生成
- 基础测试

🔄 **待完成** (需要您提供接口信息):
- 风控问询接口对接
- 风控确认接口对接
- 认证机制实现
- 完整的业务流程

请提供上述接口信息，我将继续完善整个对接系统。
